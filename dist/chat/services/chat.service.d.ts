import { ConversationManagerService } from './conversation-manager.service';
import { ChatErrorHandlerService } from './chat-error-handler.service';
import { LlmFailoverService } from './llm-failover.service';
import { ChatPerformanceMonitorService } from './chat-performance-monitor.service';
import { ChatCacheService } from './chat-cache.service';
import { EnhancedIntentClassificationService } from './enhanced-intent-classification.service';
import { ConversationFlowManagerService } from './conversation-flow-manager.service';
import { IntelligentResponseGeneratorService } from './intelligent-response-generator.service';
import { AdvancedEntityRankingService } from '../../common/ranking/advanced-entity-ranking.service';
import { PerformanceOptimizationService } from '../../common/performance/performance-optimization.service';
import { EntitiesService } from '../../entities/entities.service';
import { ILlmService } from '../../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../../common/llm/services/llm-factory.service';
import { SendChatMessageDto } from '../dto/send-chat-message.dto';
import { ChatResponseDto } from '../dto/chat-response.dto';
import { ConversationHistoryResponseDto } from '../dto/conversation-history-response.dto';
import { GetConversationHistoryDto } from '../dto/get-conversation-history.dto';
export declare class ChatService {
    private readonly conversationManager;
    private readonly chatErrorHandler;
    private readonly llmFailover;
    private readonly performanceMonitor;
    private readonly cacheService;
    private readonly enhancedIntentService;
    private readonly conversationFlowManager;
    private readonly intelligentResponseGenerator;
    private readonly advancedRankingService;
    private readonly performanceOptimizationService;
    private readonly entitiesService;
    private readonly llmService;
    private readonly llmFactoryService;
    private readonly logger;
    constructor(conversationManager: ConversationManagerService, chatErrorHandler: ChatErrorHandlerService, llmFailover: LlmFailoverService, performanceMonitor: ChatPerformanceMonitorService, cacheService: ChatCacheService, enhancedIntentService: EnhancedIntentClassificationService, conversationFlowManager: ConversationFlowManagerService, intelligentResponseGenerator: IntelligentResponseGeneratorService, advancedRankingService: AdvancedEntityRankingService, performanceOptimizationService: PerformanceOptimizationService, entitiesService: EntitiesService, llmService: ILlmService, llmFactoryService: LlmFactoryService);
    sendMessage(userId: string, sendChatMessageDto: SendChatMessageDto): Promise<ChatResponseDto>;
    getConversationHistory(userId: string, sessionId: string, getHistoryDto: GetConversationHistoryDto): Promise<ConversationHistoryResponseDto>;
    endConversation(userId: string, sessionId: string): Promise<void>;
    getUserActiveSessions(userId: string): Promise<string[]>;
    private discoverRelevantEntities;
    private buildEntitySearchFilters;
    private applyAdvancedRanking;
    private applyFilterCorrections;
    private convertIntelligentResponseToChatResponse;
    getPerformanceMetrics(): any;
    getPerformanceHealth(): any;
    getCacheStats(): any;
    clearCaches(): void;
    private getCurrentLlmProvider;
}
