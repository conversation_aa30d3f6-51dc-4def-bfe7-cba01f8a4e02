"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var QueryOptimizationService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryOptimizationService = void 0;
const common_1 = require("@nestjs/common");
let QueryOptimizationService = QueryOptimizationService_1 = class QueryOptimizationService {
    constructor() {
        this.logger = new common_1.Logger(QueryOptimizationService_1.name);
        this.queryMetrics = new Map();
    }
    optimizeFilterOrder(filters) {
        const optimized = { ...filters };
        const filterSelectivity = this.calculateFilterSelectivity(filters);
        this.logger.debug('Filter selectivity analysis', filterSelectivity);
        return this.applySelectivityOptimizations(optimized, filterSelectivity);
    }
    generateQueryHints(filters) {
        const hints = {
            useIndex: [],
            joinOrder: [],
            filterStrategy: 'default',
            estimatedComplexity: 'low',
        };
        const complexity = this.analyzeQueryComplexity(filters);
        hints.estimatedComplexity = complexity;
        hints.useIndex = this.suggestOptimalIndexes(filters);
        hints.filterStrategy = this.determineFilterStrategy(filters, complexity);
        hints.joinOrder = this.optimizeJoinOrder(filters);
        return hints;
    }
    optimizeBatchQueries(filterSets) {
        const plan = {
            batches: [],
            estimatedTime: 0,
            cacheStrategy: 'none',
        };
        const groupedQueries = this.groupSimilarQueries(filterSets);
        plan.batches = groupedQueries.map(group => ({
            filters: group,
            priority: this.calculateBatchPriority(group),
            estimatedTime: this.estimateBatchTime(group),
        }));
        plan.batches.sort((a, b) => b.priority - a.priority);
        plan.estimatedTime = plan.batches.reduce((sum, batch) => sum + batch.estimatedTime, 0);
        plan.cacheStrategy = this.determineCacheStrategy(filterSets);
        return plan;
    }
    recordQueryPerformance(querySignature, executionTime) {
        const existing = this.queryMetrics.get(querySignature);
        if (existing) {
            existing.count++;
            existing.totalTime += executionTime;
            existing.avgTime = existing.totalTime / existing.count;
            existing.lastExecuted = new Date();
        }
        else {
            this.queryMetrics.set(querySignature, {
                count: 1,
                totalTime: executionTime,
                avgTime: executionTime,
                lastExecuted: new Date(),
            });
        }
        if (executionTime > 1000) {
            this.logger.warn(`Slow query detected: ${querySignature} (${executionTime}ms)`);
        }
    }
    getQueryPerformanceInsights() {
        const metrics = Array.from(this.queryMetrics.entries()).map(([signature, data]) => ({
            signature,
            ...data,
        }));
        const slowestQueries = metrics
            .sort((a, b) => b.avgTime - a.avgTime)
            .slice(0, 10);
        const mostFrequentQueries = metrics
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        const totalQueries = metrics.reduce((sum, m) => sum + m.count, 0);
        const avgResponseTime = metrics.reduce((sum, m) => sum + m.avgTime, 0) / metrics.length;
        return {
            totalQueries,
            avgResponseTime,
            slowestQueries,
            mostFrequentQueries,
            recommendations: this.generateOptimizationRecommendations(metrics),
        };
    }
    calculateFilterSelectivity(filters) {
        const selectivity = {};
        if (filters.entityTypeIds?.length) {
            selectivity.entityTypeIds = filters.entityTypeIds.length === 1 ? 0.1 : 0.3;
        }
        if (filters.searchTerm) {
            selectivity.searchTerm = 0.2;
        }
        if (filters.categoryIds?.length) {
            selectivity.categoryIds = 0.25;
        }
        if (filters.technical_levels?.length) {
            selectivity.technical_levels = 0.4;
        }
        if (filters.has_free_tier !== undefined) {
            selectivity.has_free_tier = 0.5;
        }
        if (filters.has_api !== undefined) {
            selectivity.has_api = 0.3;
        }
        if (filters.price_ranges?.length) {
            selectivity.price_ranges = 0.35;
        }
        return selectivity;
    }
    applySelectivityOptimizations(filters, selectivity) {
        return {
            ...filters,
            _optimizationHints: {
                selectivity,
                recommendedOrder: Object.entries(selectivity)
                    .sort(([, a], [, b]) => a - b)
                    .map(([key]) => key),
            },
        };
    }
    analyzeQueryComplexity(filters) {
        let complexity = 0;
        const activeFilters = Object.values(filters).filter(v => v !== undefined && v !== null && v !== '').length;
        complexity += activeFilters;
        if (filters.entityTypeIds?.length > 1)
            complexity += 1;
        if (filters.categoryIds?.length > 1)
            complexity += 1;
        if (filters.technical_levels?.length > 1)
            complexity += 1;
        if (filters.searchTerm)
            complexity += 2;
        if (filters.price_min || filters.price_max)
            complexity += 1;
        if (filters.salary_min || filters.salary_max)
            complexity += 1;
        if (complexity <= 3)
            return 'low';
        if (complexity <= 6)
            return 'medium';
        if (complexity <= 10)
            return 'high';
        return 'very_high';
    }
    suggestOptimalIndexes(filters) {
        const indexes = [];
        if (filters.entityTypeIds) {
            indexes.push('idx_entities_entity_type_id');
        }
        if (filters.searchTerm) {
            indexes.push('idx_entities_fts_document');
        }
        if (filters.categoryIds) {
            indexes.push('idx_entity_categories_category_id');
        }
        if (filters.has_free_tier !== undefined) {
            indexes.push('idx_entities_has_free_tier');
        }
        if (filters.technical_levels) {
            indexes.push('idx_entity_details_technical_level');
        }
        return indexes;
    }
    determineFilterStrategy(filters, complexity) {
        if (complexity === 'low') {
            return 'simple_where';
        }
        if (filters.searchTerm && complexity === 'medium') {
            return 'fts_first';
        }
        if (complexity === 'high' || complexity === 'very_high') {
            return 'staged_filtering';
        }
        return 'default';
    }
    optimizeJoinOrder(filters) {
        const joins = ['entities'];
        if (filters.entityTypeIds) {
            joins.push('entity_types');
        }
        if (filters.categoryIds) {
            joins.push('entity_categories', 'categories');
        }
        if (filters.technical_levels || filters.skill_levels) {
            joins.push('entity_details');
        }
        return joins;
    }
    groupSimilarQueries(filterSets) {
        const groups = [];
        filterSets.forEach(filters => {
            const existingGroup = groups.find(group => this.queriesAreSimilar(group[0], filters));
            if (existingGroup) {
                existingGroup.push(filters);
            }
            else {
                groups.push([filters]);
            }
        });
        return groups;
    }
    queriesAreSimilar(a, b) {
        const aEntityTypes = a.entityTypeIds?.join(',') || '';
        const bEntityTypes = b.entityTypeIds?.join(',') || '';
        return aEntityTypes === bEntityTypes &&
            (a.searchTerm || '') === (b.searchTerm || '');
    }
    calculateBatchPriority(group) {
        let priority = group.length * 10;
        const complexity = this.analyzeQueryComplexity(group[0]);
        switch (complexity) {
            case 'low':
                priority += 20;
                break;
            case 'medium':
                priority += 10;
                break;
            case 'high':
                priority += 5;
                break;
            case 'very_high':
                priority += 0;
                break;
        }
        return priority;
    }
    estimateBatchTime(group) {
        const baseTime = 100;
        const complexity = this.analyzeQueryComplexity(group[0]);
        let multiplier = 1;
        switch (complexity) {
            case 'low':
                multiplier = 1;
                break;
            case 'medium':
                multiplier = 1.5;
                break;
            case 'high':
                multiplier = 2;
                break;
            case 'very_high':
                multiplier = 3;
                break;
        }
        return baseTime * multiplier * group.length;
    }
    determineCacheStrategy(filterSets) {
        const uniqueQueries = new Set(filterSets.map(f => JSON.stringify(f))).size;
        const totalQueries = filterSets.length;
        const duplicateRatio = (totalQueries - uniqueQueries) / totalQueries;
        if (duplicateRatio > 0.5) {
            return 'aggressive';
        }
        else if (duplicateRatio > 0.2) {
            return 'moderate';
        }
        else {
            return 'minimal';
        }
    }
    generateOptimizationRecommendations(metrics) {
        const recommendations = [];
        const slowQueries = metrics.filter(m => m.avgTime > 500);
        if (slowQueries.length > 0) {
            recommendations.push(`Consider optimizing ${slowQueries.length} slow queries`);
        }
        const frequentQueries = metrics.filter(m => m.count > 100);
        if (frequentQueries.length > 0) {
            recommendations.push(`Consider caching results for ${frequentQueries.length} frequent queries`);
        }
        if (metrics.length > 50) {
            recommendations.push('Consider implementing query result caching');
        }
        return recommendations;
    }
};
exports.QueryOptimizationService = QueryOptimizationService;
exports.QueryOptimizationService = QueryOptimizationService = QueryOptimizationService_1 = __decorate([
    (0, common_1.Injectable)()
], QueryOptimizationService);
//# sourceMappingURL=query-optimization.service.js.map