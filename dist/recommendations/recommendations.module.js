"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsModule = void 0;
const common_1 = require("@nestjs/common");
const recommendations_controller_1 = require("./recommendations.controller");
const recommendations_service_1 = require("./recommendations.service");
const filter_extraction_service_1 = require("./services/filter-extraction.service");
const advanced_entity_ranking_service_1 = require("../common/ranking/advanced-entity-ranking.service");
const performance_optimization_service_1 = require("../common/performance/performance-optimization.service");
const query_optimization_service_1 = require("../common/performance/query-optimization.service");
const entities_module_1 = require("../entities/entities.module");
const llm_module_1 = require("../common/llm/llm.module");
const auth_module_1 = require("../auth/auth.module");
let RecommendationsModule = class RecommendationsModule {
};
exports.RecommendationsModule = RecommendationsModule;
exports.RecommendationsModule = RecommendationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            entities_module_1.EntitiesModule,
            llm_module_1.LlmModule,
            auth_module_1.AuthModule,
        ],
        controllers: [recommendations_controller_1.RecommendationsController],
        providers: [
            recommendations_service_1.RecommendationsService,
            filter_extraction_service_1.FilterExtractionService,
            advanced_entity_ranking_service_1.AdvancedEntityRankingService,
            performance_optimization_service_1.PerformanceOptimizationService,
            query_optimization_service_1.QueryOptimizationService,
        ],
        exports: [recommendations_service_1.RecommendationsService],
    })
], RecommendationsModule);
//# sourceMappingURL=recommendations.module.js.map