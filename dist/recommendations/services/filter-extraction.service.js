"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var FilterExtractionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterExtractionService = void 0;
const common_1 = require("@nestjs/common");
let FilterExtractionService = FilterExtractionService_1 = class FilterExtractionService {
    constructor() {
        this.logger = new common_1.Logger(FilterExtractionService_1.name);
    }
    async extractFiltersFromDescription(description) {
        const normalizedDescription = description.toLowerCase();
        const extractedFilters = {};
        try {
            extractedFilters.entityTypeIds = this.extractEntityTypes(normalizedDescription);
            extractedFilters.technical_levels = this.extractTechnicalLevels(normalizedDescription);
            this.extractBudgetFilters(normalizedDescription, extractedFilters);
            extractedFilters.platforms = this.extractPlatforms(normalizedDescription);
            extractedFilters.frameworks = this.extractFrameworks(normalizedDescription);
            extractedFilters.use_cases_search = this.extractUseCases(normalizedDescription);
            extractedFilters.key_features_search = this.extractKeyFeatures(normalizedDescription);
            this.extractJobFilters(normalizedDescription, extractedFilters);
            this.extractCourseFilters(normalizedDescription, extractedFilters);
            this.extractEventFilters(normalizedDescription, extractedFilters);
            this.extractHardwareFilters(normalizedDescription, extractedFilters);
            this.logger.debug('Extracted filters from description:', {
                description: description.substring(0, 100),
                extractedFilters: Object.keys(extractedFilters),
            });
            return extractedFilters;
        }
        catch (error) {
            this.logger.error('Error extracting filters from description', error.stack);
            return {};
        }
    }
    extractEntityTypes(description) {
        const entityTypes = [];
        if (this.matchesAny(description, [
            'tool', 'software', 'platform', 'api', 'service', 'application', 'app'
        ])) {
            entityTypes.push('ai-tool');
        }
        if (this.matchesAny(description, [
            'learn', 'course', 'tutorial', 'education', 'training', 'class', 'lesson'
        ])) {
            entityTypes.push('course');
        }
        if (this.matchesAny(description, [
            'job', 'position', 'career', 'work', 'employment', 'hire', 'role'
        ])) {
            entityTypes.push('job');
        }
        if (this.matchesAny(description, [
            'conference', 'event', 'meetup', 'workshop', 'webinar', 'summit'
        ])) {
            entityTypes.push('event');
        }
        if (this.matchesAny(description, [
            'gpu', 'hardware', 'computer', 'server', 'chip', 'processor'
        ])) {
            entityTypes.push('hardware');
        }
        return entityTypes;
    }
    extractTechnicalLevels(description) {
        const levels = [];
        if (this.matchesAny(description, ['beginner', 'new', 'start', 'basic', 'simple'])) {
            levels.push('BEGINNER');
        }
        if (this.matchesAny(description, ['intermediate', 'some experience', 'moderate'])) {
            levels.push('INTERMEDIATE');
        }
        if (this.matchesAny(description, ['advanced', 'expert', 'professional', 'complex'])) {
            levels.push('ADVANCED');
        }
        if (this.matchesAny(description, ['expert', 'master', 'senior', 'lead'])) {
            levels.push('EXPERT');
        }
        return levels;
    }
    extractBudgetFilters(description, filters) {
        if (this.matchesAny(description, ['free', 'no cost', 'zero cost', 'gratis'])) {
            filters.has_free_tier = true;
            filters.price_ranges = ['FREE'];
        }
        if (this.matchesAny(description, ['budget', 'cheap', 'affordable', 'low cost'])) {
            filters.price_ranges = ['FREE', 'LOW'];
        }
        const priceMatch = description.match(/\$(\d+)/);
        if (priceMatch) {
            const price = parseInt(priceMatch[1]);
            if (price <= 50) {
                filters.price_ranges = ['FREE', 'LOW'];
            }
            else if (price <= 200) {
                filters.price_ranges = ['FREE', 'LOW', 'MEDIUM'];
            }
        }
    }
    extractPlatforms(description) {
        const platforms = [];
        if (this.matchesAny(description, ['windows', 'pc']))
            platforms.push('Windows');
        if (this.matchesAny(description, ['mac', 'macos', 'apple']))
            platforms.push('macOS');
        if (this.matchesAny(description, ['linux', 'ubuntu']))
            platforms.push('Linux');
        if (this.matchesAny(description, ['web', 'browser', 'online']))
            platforms.push('Web');
        if (this.matchesAny(description, ['mobile', 'phone', 'ios', 'android']))
            platforms.push('Mobile');
        return platforms;
    }
    extractFrameworks(description) {
        const frameworks = [];
        if (this.matchesAny(description, ['tensorflow', 'tf']))
            frameworks.push('TensorFlow');
        if (this.matchesAny(description, ['pytorch', 'torch']))
            frameworks.push('PyTorch');
        if (this.matchesAny(description, ['scikit', 'sklearn']))
            frameworks.push('Scikit-learn');
        if (this.matchesAny(description, ['keras']))
            frameworks.push('Keras');
        if (this.matchesAny(description, ['opencv']))
            frameworks.push('OpenCV');
        return frameworks;
    }
    extractUseCases(description) {
        const useCases = [
            'content creation', 'data analysis', 'machine learning', 'natural language processing',
            'computer vision', 'automation', 'chatbot', 'recommendation', 'prediction',
            'classification', 'generation', 'translation', 'summarization'
        ];
        for (const useCase of useCases) {
            if (description.includes(useCase)) {
                return useCase;
            }
        }
        return '';
    }
    extractKeyFeatures(description) {
        const features = [
            'api access', 'real-time', 'batch processing', 'cloud', 'on-premise',
            'scalable', 'secure', 'fast', 'accurate', 'customizable'
        ];
        for (const feature of features) {
            if (description.includes(feature)) {
                return feature;
            }
        }
        return '';
    }
    extractJobFilters(description, filters) {
        if (this.matchesAny(description, ['full time', 'full-time'])) {
            filters.employment_types = ['FULL_TIME'];
        }
        if (this.matchesAny(description, ['part time', 'part-time'])) {
            filters.employment_types = ['PART_TIME'];
        }
        if (this.matchesAny(description, ['contract', 'contractor'])) {
            filters.employment_types = ['CONTRACT'];
        }
        if (this.matchesAny(description, ['remote', 'work from home'])) {
            filters.location_types = ['Remote'];
        }
        if (this.matchesAny(description, ['entry level', 'junior'])) {
            filters.experience_levels = ['ENTRY', 'JUNIOR'];
        }
        if (this.matchesAny(description, ['senior', 'lead'])) {
            filters.experience_levels = ['SENIOR', 'LEAD'];
        }
        const salaryMatch = description.match(/\$(\d+)k?/);
        if (salaryMatch) {
            const salary = parseInt(salaryMatch[1]);
            if (salary > 1000) {
                filters.salary_min = Math.floor(salary / 1000);
            }
            else {
                filters.salary_min = salary;
            }
        }
    }
    extractCourseFilters(description, filters) {
        if (this.matchesAny(description, ['certificate', 'certification', 'certified'])) {
            filters.certificate_available = true;
        }
        const durationMatch = description.match(/(\d+)\s*(week|month|hour)/);
        if (durationMatch) {
            filters.duration_text = `${durationMatch[1]} ${durationMatch[2]}`;
        }
    }
    extractEventFilters(description, filters) {
        if (this.matchesAny(description, ['online', 'virtual', 'webinar'])) {
            filters.is_online = true;
        }
        if (this.matchesAny(description, ['conference'])) {
            filters.event_types = ['Conference'];
        }
        if (this.matchesAny(description, ['workshop'])) {
            filters.event_types = ['Workshop'];
        }
        if (this.matchesAny(description, ['meetup'])) {
            filters.event_types = ['Meetup'];
        }
    }
    extractHardwareFilters(description, filters) {
        if (this.matchesAny(description, ['gpu', 'graphics card'])) {
            filters.hardware_types = ['GPU'];
        }
        if (this.matchesAny(description, ['cpu', 'processor'])) {
            filters.hardware_types = ['CPU'];
        }
        if (this.matchesAny(description, ['nvidia'])) {
            filters.manufacturers = ['NVIDIA'];
        }
        if (this.matchesAny(description, ['amd'])) {
            filters.manufacturers = ['AMD'];
        }
        if (this.matchesAny(description, ['intel'])) {
            filters.manufacturers = ['Intel'];
        }
        const memoryMatch = description.match(/(\d+)gb/i);
        if (memoryMatch) {
            filters.memory_search = `${memoryMatch[1]}GB`;
        }
    }
    matchesAny(text, patterns) {
        return patterns.some(pattern => text.includes(pattern));
    }
};
exports.FilterExtractionService = FilterExtractionService;
exports.FilterExtractionService = FilterExtractionService = FilterExtractionService_1 = __decorate([
    (0, common_1.Injectable)()
], FilterExtractionService);
//# sourceMappingURL=filter-extraction.service.js.map